// API configuration and utilities
export const API_URLS = {
  NODE_BACKEND: process.env.NEXT_PUBLIC_NODE_BACKEND_URL || 'http://localhost:3001',
  PYTHON_BACKEND: process.env.NEXT_PUBLIC_PYTHON_BACKEND_URL || 'http://localhost:8000',
  INSTAGRAM_API: process.env.NEXT_PUBLIC_INSTAGRAM_API_URL || 'http://localhost:3010',
};

export interface ApiResponse<T> {
  data?: T;
  error?: string;
  message?: string;
}

export class ApiError extends Error {
  constructor(
    message: string,
    public status: number,
    public response?: any
  ) {
    super(message);
    this.name = 'ApiError';
  }
}

export async function fetchWithTimeout(
  url: string,
  options: RequestInit & { timeout?: number } = {}
): Promise<Response> {
  const { timeout = 30000, ...fetchOptions } = options;
  
  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), timeout);
  
  try {
    const response = await fetch(url, {
      ...fetchOptions,
      signal: controller.signal,
    });
    
    clearTimeout(timeoutId);
    return response;
  } catch (error) {
    clearTimeout(timeoutId);
    throw error;
  }
}

export async function apiRequest<T>(
  url: string,
  options: RequestInit & { timeout?: number } = {}
): Promise<T> {
  const response = await fetchWithTimeout(url, {
    headers: {
      'Content-Type': 'application/json',
      ...options.headers,
    },
    ...options,
  });

  if (!response.ok) {
    let errorMessage = `HTTP ${response.status}`;
    try {
      const errorData = await response.json();
      errorMessage = errorData.message || errorData.error || errorMessage;
    } catch {
      // If we can't parse the error response, use the status text
      errorMessage = response.statusText || errorMessage;
    }
    throw new ApiError(errorMessage, response.status);
  }

  return response.json();
}

// Specific API functions
export const twitterApi = {
  processTweet: (tweetId: string, signal?: AbortSignal) =>
    apiRequest<{ response: string[] }>(`${API_URLS.NODE_BACKEND}/api/process-tweet`, {
      method: 'POST',
      body: JSON.stringify({ tweetId }),
      signal,
    }),
};

export const tiktokApi = {
  generateResponse: (videoUrl: string, signal?: AbortSignal) =>
    apiRequest<{ response: string[] }>(`${API_URLS.PYTHON_BACKEND}/generate_tiktok_response`, {
      method: 'POST',
      body: JSON.stringify({ video_url: videoUrl }),
      signal,
    }),
};

export const instagramApi = {
  getPost: (shortcode: string, signal?: AbortSignal) =>
    apiRequest<any>(`${API_URLS.INSTAGRAM_API}/api/instagram/p/${shortcode}`, {
      signal,
    }),
  
  generatePhotoResponse: (photoData: any, signal?: AbortSignal) =>
    apiRequest<{ response: string[] }>(`${API_URLS.PYTHON_BACKEND}/generate_instagram_response_photo`, {
      method: 'POST',
      body: JSON.stringify({ photo_data: photoData }),
      signal,
    }),
  
  downloadVideo: (url: string, filename: string, signal?: AbortSignal) =>
    apiRequest<{ path: string }>(`${API_URLS.INSTAGRAM_API}/api/download-proxy?url=${encodeURIComponent(url)}&filename=${encodeURIComponent(filename)}`, {
      signal,
    }),
  
  generateVideoResponse: (path: string, signal?: AbortSignal) =>
    apiRequest<{ response: string[] }>(`${API_URLS.PYTHON_BACKEND}/generate_instagram_response_video`, {
      method: 'POST',
      body: JSON.stringify({ path }),
      signal,
    }),
};
