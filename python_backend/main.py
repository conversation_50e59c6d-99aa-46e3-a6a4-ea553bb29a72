import uvicorn
from nyx.main import create_app

if __name__ == "__main__":
    uvicorn.run(
        "nyx.main:create_app",  # Module and function to import and run
        host="0.0.0.0",
        port=8000,
        log_level="info",
        reload=False,  # Disable reload for better concurrency in production
        workers=1,  # Keep single worker but increase concurrency
        limit_concurrency=50,  # Increased concurrent requests limit
        limit_max_requests=5000,  # Increased before worker restart
        timeout_keep_alive=30,  # Keep connections alive longer
        timeout_graceful_shutdown=30,  # Graceful shutdown timeout
    )
