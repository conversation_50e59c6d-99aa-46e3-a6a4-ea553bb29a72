import { useCallback, useRef } from 'react';

interface AsyncOperationOptions {
  onStart?: () => void;
  onSuccess?: (result: any) => void;
  onError?: (error: Error) => void;
  onFinally?: () => void;
}

export function useAsyncOperation() {
  const abortControllerRef = useRef<AbortController | null>(null);

  const execute = useCallback(async <T>(
    operation: (signal: AbortSignal) => Promise<T>,
    options: AsyncOperationOptions = {}
  ): Promise<T | null> => {
    // Cancel any existing operation
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    // Create new abort controller for this operation
    abortControllerRef.current = new AbortController();
    const signal = abortControllerRef.current.signal;

    try {
      options.onStart?.();
      
      const result = await operation(signal);
      
      // Check if operation was cancelled
      if (signal.aborted) {
        return null;
      }
      
      options.onSuccess?.(result);
      return result;
    } catch (error) {
      // Don't handle abort errors as real errors
      if (error instanceof Error && error.name === 'AbortError') {
        return null;
      }
      
      options.onError?.(error as Error);
      throw error;
    } finally {
      if (!signal.aborted) {
        options.onFinally?.();
      }
    }
  }, []);

  const cancel = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
      abortControllerRef.current = null;
    }
  }, []);

  return { execute, cancel };
}
